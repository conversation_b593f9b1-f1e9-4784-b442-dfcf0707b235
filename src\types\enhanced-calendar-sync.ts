// Enhanced Calendar Sync Type Definitions

export interface WebhookStatus {
  id: string;
  expiration: string;
  isActive: boolean;
  notificationCount: number;
  lastNotificationAt?: string;
  createdAt: string;
}

export interface WebhookSetupResponse {
  success: boolean;
  data: {
    channelId: string;
    expiration: number;
    isActive: boolean;
  };
}

export interface WebhookStatusResponse {
  success: boolean;
  data: {
    webhooks: WebhookStatus[];
    hasActiveWebhook: boolean;
  };
}

export interface SyncError {
  type: string;
  message: string;
  timestamp: string;
}

export interface SyncConflict {
  eventId: string;
  conflictType: 'time_overlap' | 'double_booking';
  conflictingWith: string[];
  suggestedResolution: string;
  newSession?: {
    fromDate: string;
    toDate: string;
    clientName?: string;
    summary?: string;
  };
  existingSession?: {
    fromDate: string;
    toDate: string;
    clientName?: string;
    summary?: string;
  };
}

export interface SyncResult {
  success: boolean;
  syncedEvents: number;
  updatedEvents: number;
  createdEvents: number;
  errors: SyncError[];
  conflicts: SyncConflict[];
}

export interface SyncStatistics {
  lastSyncTime: string;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  eventsProcessed: number;
  conflictsResolved: number;
}

export interface ExtendedSessionFormData {
  // Existing fields
  fromDate: string;
  startTime: string;
  endTime: string;
  recurrence: string;
  toDate: string;
  name: string;
  phone: string;
  emails: string[];
  age: string;
  gender: string;
  amount: string;
  reminder: string;
  summary: string;
  
  // New enhanced fields
  maxMonths: number;           // 1-24 months
  autoExtend: boolean;         // Toggle switch
  conflictResolution: 'skip' | 'warn' | 'force'; // Radio buttons
  extendThresholdDays: number; // Number input (when autoExtend enabled)
}

export interface EnhancedSessionCreationRequest {
  // ... existing fields
  clientId?: string;
  duration: number;
  recurrence: string;
  recurrenceDates: string[];
  
  // New enhanced fields
  maxMonths: number;                    // 1-24 months (default: 12)
  autoExtend: boolean;                  // Auto-extend sessions
  conflictResolution: 'skip' | 'warn' | 'force'; // Conflict handling
  extendThresholdDays: number;          // When to auto-extend
}

export interface EnhancedSessionCreationResponse {
  success: boolean;
  data: {
    schedule: {
      // ... existing schedule data
      maxMonths: number;
      autoExtend: boolean;
      totalSessionsCreated: number;
      estimatedEndDate: string;
    };
    conflicts: SyncConflict[];
    warnings: string[];
  };
}

export interface ManualSyncOptions {
  incremental?: boolean;
  maxResults?: number;
}

export interface ConflictResolution {
  eventId: string;
  resolution: 'keep_existing' | 'keep_new' | 'skip' | 'manual_review';
}

export interface RealTimeUpdate {
  type: 'SYNC_COMPLETED' | 'CONFLICT_DETECTED' | 'WEBHOOK_EXPIRED' | 'SESSION_RESCHEDULED' | 'SYNC_STARTED' | 'SYNC_ERROR';
  timestamp: string;
  data?: Record<string, unknown>;
  message?: string;
}

export interface WebSocketMessage {
  type: string;
  data: Record<string, unknown>;
  timestamp: string;
}

// Component Props Interfaces
export interface WebhookManagementProps {
  therapistId: string;
}

export interface SyncStatusIndicatorProps {
  therapistId: string;
  className?: string;
}

export interface ManualSyncButtonProps {
  onSyncComplete?: (result: SyncResult) => void;
  className?: string;
  variant?: "filledGreen" | "outlinedGreen" | "filled" | "outlined";
  size?: "sm" | "md" | "lg";
  showLastResult?: boolean;
}

export interface EnhancedConflictModalProps {
  open: boolean;
  onClose: () => void;
  message: string;
  conflicts: SyncConflict[];
  onResolve: (resolutions: ConflictResolution[]) => void;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Calendar Event Types (enhanced)
export interface CalendarEvent {
  id: string;
  summary: string;
  description?: string;
  start: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  end: {
    dateTime?: string;
    date?: string;
    timeZone?: string;
  };
  attendees?: Array<{
    email: string;
    organizer?: boolean;
    self?: boolean;
    responseStatus?: string;
  }>;
  status: string;
  eventIds?: string[];
  fromPublicCalender?: boolean;
  conflictsWith?: string[];
  syncStatus?: 'pending' | 'synced' | 'conflict' | 'error';
}

// Session Types (enhanced)
export interface EnhancedSession {
  _id: string;
  clientId?: {
    _id: string;
    name?: string;
    email?: string;
  };
  name?: string;
  email?: string;
  fromDate: string;
  toDate: string;
  tillDate?: Date;
  meetLink?: string;
  recurrenceDates?: {
    _id: string;
  };
  fromPublicCalender?: boolean;
  
  // Enhanced fields
  maxMonths?: number;
  autoExtend?: boolean;
  conflictResolution?: 'skip' | 'warn' | 'force';
  extendThresholdDays?: number;
  syncStatus?: 'pending' | 'synced' | 'conflict' | 'error';
  lastSyncTime?: string;
  conflictHistory?: SyncConflict[];
}

// Utility Types
export type ConflictType = 'time_overlap' | 'double_booking';
export type ConflictResolutionType = 'skip' | 'warn' | 'force';
export type SyncStatus = 'pending' | 'synced' | 'conflict' | 'error';
export type WebhookEventType = 'SYNC_COMPLETED' | 'CONFLICT_DETECTED' | 'WEBHOOK_EXPIRED' | 'SESSION_RESCHEDULED' | 'SYNC_STARTED' | 'SYNC_ERROR';

// Form Validation Types
export interface FormValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: FormValidationError[];
}

// Settings Types
export interface CalendarSyncSettings {
  webhookEnabled: boolean;
  autoSyncEnabled: boolean;
  conflictResolution: ConflictResolutionType;
  syncFrequency: number; // in minutes
  maxEventsPerSync: number;
  retentionPeriod: number; // in days
}

export interface TherapistSettings {
  id: string;
  calendarSync: CalendarSyncSettings;
  notifications: {
    syncComplete: boolean;
    conflictsDetected: boolean;
    webhookExpiring: boolean;
  };
}

// Error Types
export interface SyncErrorDetails extends Error {
  code: string;
  details?: Record<string, unknown>;
  timestamp: string;
  retryable: boolean;
}

export interface WebhookError extends Error {
  webhookId: string;
  errorType: 'connection' | 'authentication' | 'rate_limit' | 'server_error';
  retryAfter?: number;
}
