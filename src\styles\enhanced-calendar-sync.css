/* Enhanced Calendar Sync Styles */

/* Sync Status Indicators */
.sync-status-active {
  color: #10b981;
  animation: pulse 2s infinite;
}

.sync-status-inactive {
  color: #ef4444;
}

.sync-status-pending {
  color: #f59e0b;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Webhook Management Panel */
.webhook-panel {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.webhook-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.webhook-status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.webhook-status-indicator.active {
  background-color: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.webhook-status-indicator.inactive {
  background-color: #ef4444;
}

/* Conflict Resolution Modal */
.conflict-item {
  border: 1px solid #fecaca;
  background: #fef2f2;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.conflict-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conflict-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .conflict-comparison {
    grid-template-columns: 1fr;
  }
}

.conflict-comparison .existing {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 12px;
}

.conflict-comparison .new {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  padding: 12px;
}

.resolution-actions {
  margin-top: 16px;
}

.resolution-actions button {
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resolution-actions button:hover {
  background: #f9fafb;
  border-color: #10b981;
}

.resolution-actions button.selected {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

/* Session Duration Slider */
.duration-slider {
  width: 100%;
  margin: 16px 0;
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  outline: none;
  transition: all 0.3s ease;
}

.duration-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #10b981;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.duration-slider::-webkit-slider-thumb:hover {
  background: #059669;
  transform: scale(1.1);
}

.duration-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #10b981;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.duration-slider::-moz-range-thumb:hover {
  background: #059669;
  transform: scale(1.1);
}

/* Session Preview */
.session-preview {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  color: #0369a1;
  text-align: center;
  margin-top: 8px;
  transition: all 0.3s ease;
}

.session-preview:hover {
  background: #e0f2fe;
}

/* Auto-Extend Toggle */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #10b981;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Conflict Resolution Radio Buttons */
.conflict-resolution-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.conflict-resolution-option {
  display: flex;
  align-items: start;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.conflict-resolution-option:hover {
  background: #f9fafb;
  border-color: #10b981;
}

.conflict-resolution-option input[type="radio"]:checked + label {
  background: #f0f9ff;
  border-color: #10b981;
}

/* Real-time Updates Animation */
.real-time-update {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Sync Statistics */
.sync-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.sync-stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sync-stat-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sync-stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #10b981;
}

.sync-stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile Responsiveness */
@media (max-width: 640px) {
  .webhook-panel {
    padding: 12px;
  }
  
  .conflict-comparison {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .resolution-actions {
    flex-direction: column;
  }
  
  .resolution-actions button {
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .sync-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .conflict-resolution-options {
    grid-template-columns: 1fr;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
button:focus,
input:focus,
select:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .webhook-panel {
    border-width: 2px;
  }
  
  .conflict-item {
    border-width: 2px;
  }
  
  .session-preview {
    border-width: 2px;
  }
}
